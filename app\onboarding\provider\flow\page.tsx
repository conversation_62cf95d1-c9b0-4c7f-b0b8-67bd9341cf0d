"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON>Left, ChefHat } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { LoadingState } from "@/components/ui/loading-state";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  MultiStepForm,
  useMultiStepForm,
} from "@/components/ui/multi-step-form";
import { useUser, useIsProvider } from "@/hooks/use-auth";
import {
  useCreateProvider,
  useProviderStatus,
  type ProviderOnboardingData,
} from "@/hooks/use-provider-onboarding";
import { simpleProviderOnboardingSchema } from "@/lib/validations";

// Simplified form data type
type SimpleFormData = {
  businessName: string;
  description: string;
  serviceAreas: string;
  contactPersonName: string;
  mobileNumber: string;
};

const steps = [
  {
    id: "business-info",
    title: "Business Information",
    description: "Tell us about your catering business",
  },
  {
    id: "service-details",
    title: "Service Details",
    description: "Describe your services and coverage area",
  },
  {
    id: "contact-info",
    title: "Contact Information",
    description: "How customers can reach you",
  },
];

export default function ProviderOnboardingFlowPage() {
  const router = useRouter();
  const { data: user, isLoading: isUserLoading } = useUser();
  const { value: isProvider } = useIsProvider();

  // TanStack Query hooks
  const createProviderMutation = useCreateProvider();
  const { data: isExistingProvider, isLoading: isCheckingProvider } =
    useProviderStatus();

  // Multi-step form state
  const { currentStep, nextStep, previousStep, canGoPrevious } =
    useMultiStepForm({
      totalSteps: steps.length,
    });

  // React Hook Form setup with validation
  const form = useForm<SimpleFormData>({
    resolver: zodResolver(simpleProviderOnboardingSchema),
    defaultValues: {
      businessName: "",
      description: "",
      serviceAreas: "",
      contactPersonName: "",
      mobileNumber: "",
    },
    mode: "onChange",
  });

  const {
    watch,
    formState: { errors, dirtyFields },
  } = form;
  const watchedValues = watch();

  // Step-specific validation with better performance
  const stepValidation: Record<number, boolean> = React.useMemo(() => {
    const businessNameValid = watchedValues.businessName?.trim().length >= 2;
    const descriptionValid = watchedValues.description?.trim().length >= 10;
    const serviceAreasValid = watchedValues.serviceAreas?.trim().length > 0;
    const contactNameValid =
      watchedValues.contactPersonName?.trim().length >= 2;
    const mobileValid = watchedValues.mobileNumber?.trim().length > 0;

    return {
      1: businessNameValid && !errors.businessName,
      2:
        descriptionValid &&
        serviceAreasValid &&
        !errors.description &&
        !errors.serviceAreas,
      3:
        contactNameValid &&
        mobileValid &&
        !errors.contactPersonName &&
        !errors.mobileNumber,
    };
  }, [watchedValues, errors]);

  // Form persistence to localStorage
  React.useEffect(() => {
    const subscription = watch((value) => {
      if (Object.keys(dirtyFields).length > 0) {
        localStorage.setItem("onboarding-form-data", JSON.stringify(value));
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, dirtyFields]);

  // Enhanced state persistence and recovery
  const [showRecoveryPrompt, setShowRecoveryPrompt] = React.useState(false);
  const [savedFormData, setSavedFormData] =
    React.useState<SimpleFormData | null>(null);

  // Load saved form data on mount with recovery prompt
  React.useEffect(() => {
    const savedData = localStorage.getItem("onboarding-form-data");
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        const savedTimestamp = localStorage.getItem(
          "onboarding-form-timestamp"
        );
        const isRecent =
          savedTimestamp &&
          Date.now() - parseInt(savedTimestamp) < 24 * 60 * 60 * 1000; // 24 hours

        if (isRecent) {
          setSavedFormData(parsedData);
          setShowRecoveryPrompt(true);
        } else {
          // Clear old data
          localStorage.removeItem("onboarding-form-data");
          localStorage.removeItem("onboarding-form-timestamp");
        }
      } catch (error) {
        console.warn("Failed to load saved form data:", error);
        localStorage.removeItem("onboarding-form-data");
        localStorage.removeItem("onboarding-form-timestamp");
      }
    }
  }, []);

  // Recovery functions
  const handleRecoverData = React.useCallback(() => {
    if (savedFormData) {
      form.reset(savedFormData);
      setShowRecoveryPrompt(false);
      toast.success("Form data recovered successfully!");
    }
  }, [savedFormData, form]);

  const handleDiscardData = React.useCallback(() => {
    localStorage.removeItem("onboarding-form-data");
    localStorage.removeItem("onboarding-form-timestamp");
    setShowRecoveryPrompt(false);
    setSavedFormData(null);
  }, []);

  // Enhanced form persistence with timestamp
  React.useEffect(() => {
    const subscription = watch((value) => {
      if (Object.keys(dirtyFields).length > 0) {
        localStorage.setItem("onboarding-form-data", JSON.stringify(value));
        localStorage.setItem(
          "onboarding-form-timestamp",
          Date.now().toString()
        );
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, dirtyFields]);

  // Memoized step content components using react-hook-form
  const BusinessInfoStep = React.useMemo(
    () => (
      <Form {...form}>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="businessName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Business Name <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="Enter your business name" {...field} />
                </FormControl>
                <FormDescription>
                  The official name of your catering business
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </Form>
    ),
    [form]
  );

  const ServiceDetailsStep = React.useMemo(
    () => (
      <Form {...form}>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Service Description <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe your catering services..."
                    rows={4}
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Tell customers about your catering services and specialties
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="serviceAreas"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Service Areas <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="List the areas you serve (e.g., Manila, Quezon City, Makati)"
                    rows={3}
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Comma-separated list of areas where you provide catering
                  services
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </Form>
    ),
    [form]
  );

  const ContactInfoStep = React.useMemo(
    () => (
      <Form {...form}>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="contactPersonName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Contact Person Name <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="Enter contact person name" {...field} />
                </FormControl>
                <FormDescription>
                  The main contact person for your catering business
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="mobileNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Mobile Number <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder="Enter your mobile number"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Your primary contact number for customer inquiries
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </Form>
    ),
    [form]
  );

  // Optimized step content rendering
  const renderStepContent = React.useCallback(() => {
    switch (currentStep) {
      case 1:
        return BusinessInfoStep;
      case 2:
        return ServiceDetailsStep;
      case 3:
        return ContactInfoStep;
      default:
        return null;
    }
  }, [currentStep, BusinessInfoStep, ServiceDetailsStep, ContactInfoStep]);

  // Memoized handlers to prevent unnecessary re-renders - MUST be before any conditional returns
  const handleNext = React.useCallback(() => {
    if (stepValidation[currentStep]) {
      nextStep();
    } else {
      // More specific error messages based on current step
      const stepNames = [
        "Business Information",
        "Service Details",
        "Contact Information",
      ];
      toast.error(
        `Please complete all required fields in ${
          stepNames[currentStep - 1]
        } before continuing.`
      );
    }
  }, [stepValidation, currentStep, nextStep]);

  // Enhanced form submission with react-hook-form validation
  const handleSubmit = React.useCallback(async () => {
    if (!stepValidation[currentStep]) {
      toast.error("Please complete all required fields.");
      return;
    }

    if (isExistingProvider) {
      toast.error("You are already a catering provider.");
      router.push("/dashboard");
      return;
    }

    // Validate all steps before submission
    const allStepsValid = Object.values(stepValidation).every(Boolean);
    if (!allStepsValid) {
      const invalidSteps = Object.entries(stepValidation)
        .filter(([, isValid]) => !isValid)
        .map(([step]) => {
          const stepNames = [
            "Business Information",
            "Service Details",
            "Contact Information",
          ];
          return stepNames[parseInt(step) - 1];
        });
      toast.error(`Please complete: ${invalidSteps.join(", ")}`);
      return;
    }

    // Get form data and validate
    const formData = form.getValues();
    const validationResult = simpleProviderOnboardingSchema.safeParse(formData);

    if (!validationResult.success) {
      toast.error("Please fix the validation errors before submitting.");
      return;
    }

    // Convert form data to match backend expectations with validation
    const serviceAreasArray = formData.serviceAreas
      .split(",")
      .map((area: string) => area.trim())
      .filter((area: string) => area.length > 0);

    if (serviceAreasArray.length === 0) {
      toast.error("Please provide at least one service area.");
      return;
    }

    const submissionData: ProviderOnboardingData = {
      businessName: formData.businessName.trim(),
      description: formData.description.trim(),
      serviceAreas: serviceAreasArray,
      contactPersonName: formData.contactPersonName.trim(),
      mobileNumber: formData.mobileNumber.trim(),
    };

    createProviderMutation.mutate(submissionData, {
      onSuccess: () => {
        // Clear saved form data on successful submission
        localStorage.removeItem("onboarding-form-data");

        toast.success(
          "🎉 Onboarding completed successfully! Welcome to CateringHub!"
        );
        router.push("/dashboard");
      },
      onError: (error) => {
        console.error("Error submitting onboarding:", error);

        // More specific error messages
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        if (errorMessage.includes("duplicate")) {
          toast.error(
            "You already have a provider profile. Redirecting to dashboard..."
          );
          setTimeout(() => router.push("/dashboard"), 2000);
        } else if (
          errorMessage.includes("network") ||
          errorMessage.includes("fetch")
        ) {
          toast.error(
            "Network error. Please check your connection and try again."
          );
        } else {
          toast.error(
            "Submission failed. Please check your information and try again."
          );
        }
      },
    });
  }, [
    stepValidation,
    currentStep,
    isExistingProvider,
    form,
    createProviderMutation,
    router,
  ]);

  // Simple redirect logic
  React.useEffect(() => {
    if (isUserLoading || isCheckingProvider) return;

    if (!user) {
      router.push(
        "/login?redirect=" + encodeURIComponent("/onboarding/provider/flow")
      );
      return;
    }

    if (isProvider || isExistingProvider) {
      router.push("/dashboard");
      return;
    }
  }, [
    user,
    isUserLoading,
    isProvider,
    isExistingProvider,
    isCheckingProvider,
    router,
  ]);

  // Show loading state with skeleton loaders
  if (isUserLoading || isCheckingProvider) {
    return (
      <div className="min-h-screen bg-background">
        {/* Header Skeleton */}
        <header className="border-b border-border">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-6 w-6 bg-muted rounded animate-pulse"></div>
              <div className="h-6 w-32 bg-muted rounded animate-pulse"></div>
            </div>
            <div className="h-9 w-20 bg-muted rounded animate-pulse"></div>
          </div>
        </header>

        {/* Main Content Skeleton */}
        <main className="container mx-auto px-4 py-8">
          <div className="w-full max-w-4xl mx-auto">
            {/* Progress Steps Skeleton */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                {[1, 2, 3].map((step) => (
                  <div
                    key={step}
                    className="flex flex-col items-center space-y-2"
                  >
                    <div className="w-8 h-8 bg-muted rounded-full animate-pulse"></div>
                    <div className="h-4 w-20 bg-muted rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Form Card Skeleton */}
            <LoadingState variant="card" count={1} showFooter={true} />
          </div>
        </main>
      </div>
    );
  }

  // Don't render if user is not logged in (will redirect)
  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Recovery Prompt */}
      {showRecoveryPrompt && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-2">
              Recover Previous Data?
            </h3>
            <p className="text-gray-600 mb-4">
              We found some previously saved form data. Would you like to
              recover it?
            </p>
            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={handleDiscardData}>
                Start Fresh
              </Button>
              <Button onClick={handleRecoverData}>Recover Data</Button>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ChefHat className="h-6 w-6" />
            <Typography variant="h5">CateringHub</Typography>
          </div>

          <Button variant="ghost" asChild>
            <Link
              href="/onboarding/provider"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Link>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <MultiStepForm
          steps={steps}
          currentStep={currentStep}
          onNext={handleNext}
          onPrevious={previousStep}
          onSubmit={handleSubmit}
          canGoNext={stepValidation[currentStep] || false}
          canGoPrevious={canGoPrevious}
          isSubmitting={createProviderMutation.isPending}
          title="Provider Onboarding"
          description="Complete your catering provider profile to start accepting bookings"
          showProgress={true}
          progressOrientation="horizontal"
        >
          {renderStepContent()}
        </MultiStepForm>
      </main>
    </div>
  );
}
